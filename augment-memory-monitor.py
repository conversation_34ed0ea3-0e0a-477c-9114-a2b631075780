#!/usr/bin/env python3
"""
Augment Memory Monitor - Live Cloning System
Monitors Augment's memory system and automatically clones new memories to OpenMemory
"""

import time
import json
import requests
import sqlite3
import os
import hashlib
from datetime import datetime
from typing import Set, Dict, List, Any
from pathlib import Path
import threading
import signal
import sys

class AugmentMemoryMonitor:
    def __init__(self):
        self.openmemory_url = "http://localhost:8765"
        self.user_id = "deepakbatham"
        self.monitoring = False
        self.seen_memories = set()
        self.check_interval = 30  # seconds
        self.augment_data_paths = self._find_augment_data_paths()
        
    def _find_augment_data_paths(self) -> List[str]:
        """Find potential Augment data storage locations"""
        possible_paths = [
            # Common Augment data locations
            os.path.expanduser("~/.augment/"),
            os.path.expanduser("~/Library/Application Support/Augment/"),
            os.path.expanduser("~/AppData/Local/Augment/"),
            os.path.expanduser("~/Documents/Augment/"),
            # Project-specific locations
            "./augment_data/",
            "./.augment/",
            # VSCode/IDE specific
            os.path.expanduser("~/.vscode/extensions/"),
            os.path.expanduser("~/Library/Application Support/Code/User/"),
        ]
        
        existing_paths = []
        for path in possible_paths:
            if os.path.exists(path):
                existing_paths.append(path)
        
        return existing_paths
    
    def _get_memory_hash(self, content: str) -> str:
        """Generate a hash for memory content to detect duplicates"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def _scan_sqlite_databases(self) -> List[Dict[str, Any]]:
        """Scan SQLite databases for new memories"""
        new_memories = []
        
        for base_path in self.augment_data_paths:
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.endswith(('.db', '.sqlite', '.sqlite3')):
                        db_path = os.path.join(root, file)
                        try:
                            memories = self._extract_from_sqlite(db_path)
                            new_memories.extend(memories)
                        except Exception as e:
                            print(f"⚠️  Error scanning {db_path}: {e}")
        
        return new_memories
    
    def _extract_from_sqlite(self, db_path: str) -> List[Dict[str, Any]]:
        """Extract memories from a SQLite database"""
        memories = []
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            # Look for memory-related tables
            memory_tables = [t for t in tables if any(keyword in t.lower() 
                           for keyword in ['memory', 'entity', 'node', 'knowledge', 'graph', 'context'])]
            
            for table in memory_tables:
                try:
                    cursor.execute(f"SELECT * FROM {table}")
                    rows = cursor.fetchall()
                    
                    # Get column names
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    for row in rows:
                        row_dict = dict(zip(columns, row))
                        memory = self._process_database_row(table, row_dict, db_path)
                        if memory:
                            memories.append(memory)
                            
                except Exception as e:
                    print(f"⚠️  Error reading table {table}: {e}")
            
            conn.close()
            
        except Exception as e:
            print(f"⚠️  Error accessing database {db_path}: {e}")
        
        return memories
    
    def _process_database_row(self, table_name: str, row_data: Dict[str, Any], db_path: str) -> Dict[str, Any]:
        """Process a database row into a memory object"""
        # Extract meaningful content
        content_fields = ['content', 'text', 'description', 'value', 'memory', 'observation']
        name_fields = ['name', 'title', 'label', 'entity_name']
        
        content = None
        name = None
        
        for field in content_fields:
            if field in row_data and row_data[field]:
                content = str(row_data[field])
                break
        
        for field in name_fields:
            if field in row_data and row_data[field]:
                name = str(row_data[field])
                break
        
        if not content and not name:
            return None
        
        # Create memory object
        memory_content = content or name or str(row_data)
        memory_hash = self._get_memory_hash(memory_content)
        
        # Skip if we've already seen this memory
        if memory_hash in self.seen_memories:
            return None
        
        return {
            "content": memory_content,
            "name": name or f"Memory_{memory_hash[:8]}",
            "source": f"sqlite_{table_name}",
            "db_path": db_path,
            "hash": memory_hash,
            "raw_data": row_data,
            "discovered_at": datetime.now().isoformat()
        }
    
    def _scan_json_files(self) -> List[Dict[str, Any]]:
        """Scan JSON files for new memories"""
        new_memories = []
        
        for base_path in self.augment_data_paths:
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.endswith('.json') and 'memory' in file.lower():
                        json_path = os.path.join(root, file)
                        try:
                            memories = self._extract_from_json(json_path)
                            new_memories.extend(memories)
                        except Exception as e:
                            print(f"⚠️  Error scanning {json_path}: {e}")
        
        return new_memories
    
    def _extract_from_json(self, json_path: str) -> List[Dict[str, Any]]:
        """Extract memories from JSON files"""
        memories = []
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Process different JSON structures
            if isinstance(data, dict):
                if 'entities' in data:
                    for entity in data['entities']:
                        memory = self._process_json_entity(entity, json_path)
                        if memory:
                            memories.append(memory)
                
                if 'memories' in data:
                    for memory_item in data['memories']:
                        memory = self._process_json_memory(memory_item, json_path)
                        if memory:
                            memories.append(memory)
            
            elif isinstance(data, list):
                for item in data:
                    memory = self._process_json_item(item, json_path)
                    if memory:
                        memories.append(memory)
        
        except Exception as e:
            print(f"⚠️  Error reading JSON {json_path}: {e}")
        
        return memories
    
    def _process_json_entity(self, entity: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Process a JSON entity into a memory"""
        name = entity.get('name', 'Unknown')
        observations = entity.get('observations', [])
        
        if not observations:
            return None
        
        content = f"Entity: {name}\n\nObservations:\n" + "\n".join(f"- {obs}" for obs in observations)
        memory_hash = self._get_memory_hash(content)
        
        if memory_hash in self.seen_memories:
            return None
        
        return {
            "content": content,
            "name": name,
            "source": "json_entity",
            "file_path": file_path,
            "hash": memory_hash,
            "discovered_at": datetime.now().isoformat()
        }
    
    def _process_json_memory(self, memory_item: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Process a JSON memory item"""
        content = memory_item.get('content', memory_item.get('text', str(memory_item)))
        memory_hash = self._get_memory_hash(content)
        
        if memory_hash in self.seen_memories:
            return None
        
        return {
            "content": content,
            "name": memory_item.get('name', f"Memory_{memory_hash[:8]}"),
            "source": "json_memory",
            "file_path": file_path,
            "hash": memory_hash,
            "discovered_at": datetime.now().isoformat()
        }
    
    def _process_json_item(self, item: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """Process a generic JSON item"""
        if not isinstance(item, dict):
            return None
        
        content = item.get('content', item.get('text', item.get('description', str(item))))
        memory_hash = self._get_memory_hash(content)
        
        if memory_hash in self.seen_memories:
            return None
        
        return {
            "content": content,
            "name": item.get('name', item.get('title', f"Item_{memory_hash[:8]}")),
            "source": "json_item",
            "file_path": file_path,
            "hash": memory_hash,
            "discovered_at": datetime.now().isoformat()
        }
    
    def _add_to_openmemory(self, memory: Dict[str, Any]) -> bool:
        """Add a memory to OpenMemory"""
        url = f"{self.openmemory_url}/api/v1/memories/"
        
        payload = {
            "user_id": self.user_id,
            "text": memory["content"],
            "metadata": {
                "source": "augment_monitor",
                "original_source": memory["source"],
                "discovered_at": memory["discovered_at"],
                "auto_cloned": True,
                "memory_hash": memory["hash"]
            },
            "app": "augment_monitor"
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            if response.status_code in [200, 201]:
                return True
            else:
                print(f"❌ Failed to add memory: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error adding memory: {e}")
            return False
    
    def _load_seen_memories(self):
        """Load previously seen memory hashes"""
        seen_file = "seen_memories.json"
        if os.path.exists(seen_file):
            try:
                with open(seen_file, 'r') as f:
                    self.seen_memories = set(json.load(f))
                print(f"📚 Loaded {len(self.seen_memories)} previously seen memories")
            except Exception as e:
                print(f"⚠️  Error loading seen memories: {e}")
    
    def _save_seen_memories(self):
        """Save seen memory hashes"""
        seen_file = "seen_memories.json"
        try:
            with open(seen_file, 'w') as f:
                json.dump(list(self.seen_memories), f)
        except Exception as e:
            print(f"⚠️  Error saving seen memories: {e}")
    
    def _monitor_cycle(self):
        """Single monitoring cycle"""
        print(f"🔍 Scanning for new Augment memories... ({datetime.now().strftime('%H:%M:%S')})")
        
        # Scan SQLite databases
        sqlite_memories = self._scan_sqlite_databases()
        
        # Scan JSON files
        json_memories = self._scan_json_files()
        
        all_new_memories = sqlite_memories + json_memories
        
        if not all_new_memories:
            print("   No new memories found")
            return
        
        print(f"📊 Found {len(all_new_memories)} new memories")
        
        # Add new memories to OpenMemory
        added_count = 0
        for memory in all_new_memories:
            if self._add_to_openmemory(memory):
                self.seen_memories.add(memory["hash"])
                added_count += 1
                print(f"✅ Cloned: {memory['name'][:50]}...")
            else:
                print(f"❌ Failed: {memory['name'][:50]}...")
            
            time.sleep(1)  # Rate limiting
        
        print(f"📈 Successfully cloned {added_count}/{len(all_new_memories)} memories")
        
        # Save seen memories
        self._save_seen_memories()
    
    def start_monitoring(self):
        """Start the monitoring loop"""
        print("🚀 Starting Augment Memory Monitor")
        print(f"📁 Monitoring paths: {self.augment_data_paths}")
        print(f"⏱️  Check interval: {self.check_interval} seconds")
        print(f"🔗 OpenMemory URL: {self.openmemory_url}")
        print("=" * 60)
        
        # Load previously seen memories
        self._load_seen_memories()
        
        # Check OpenMemory connection
        try:
            response = requests.get(f"{self.openmemory_url}/docs", timeout=5)
            if response.status_code != 200:
                print("❌ Cannot connect to OpenMemory. Make sure it's running.")
                return False
        except:
            print("❌ Cannot connect to OpenMemory. Make sure it's running.")
            return False
        
        print("✅ Connected to OpenMemory")
        
        self.monitoring = True
        
        # Set up signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            print("\n🛑 Stopping monitor...")
            self.monitoring = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Main monitoring loop
        try:
            while self.monitoring:
                self._monitor_cycle()
                
                # Wait for next cycle
                for _ in range(self.check_interval):
                    if not self.monitoring:
                        break
                    time.sleep(1)
        
        except KeyboardInterrupt:
            print("\n🛑 Monitor stopped by user")
        
        finally:
            self._save_seen_memories()
            print("💾 Saved monitoring state")
        
        return True

def main():
    print("🔄 Augment Memory Monitor - Live Cloning System")
    print("=" * 60)
    
    monitor = AugmentMemoryMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--scan-once":
        print("🔍 Running single scan...")
        monitor._load_seen_memories()
        monitor._monitor_cycle()
        print("✅ Single scan completed")
    else:
        print("🔄 Starting continuous monitoring...")
        print("💡 Press Ctrl+C to stop")
        monitor.start_monitoring()

if __name__ == "__main__":
    main()
