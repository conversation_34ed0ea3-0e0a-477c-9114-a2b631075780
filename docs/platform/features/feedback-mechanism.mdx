---
title: Feedback Mechanism
icon: "thumbs-up"
iconType: "solid"
---

<Snippet file="paper-release.mdx" />

Mem0's **Feedback Mechanism** allows you to provide feedback on the memories generated by your application. This feedback is used to improve the accuracy of the memories and the search results.

## How it works

The feedback mechanism is a simple API that allows you to provide feedback on the memories generated by your application. The feedback is stored in the database and is used to improve the accuracy of the memories and the search results. Over time, Mem0 continuously learns from this feedback, refining its memory generation and search capabilities for better performance.

## Give Feedback

You can give feedback on a memory by calling the `feedback` method on the Mem0 client.

<CodeGroup>

```python Python
from mem0 import MemoryClient

client = MemoryClient(api_key="your_api_key")

client.feedback(memory_id="your-memory-id", feedback="NEGATIVE", feedback_reason="I don't like this memory because it is not relevant.")
```

```javascript JavaScript
import MemoryClient from 'mem0ai';

const client = new MemoryClient({ apiKey: 'your-api-key'});

client.feedback({
    memory_id: "your-memory-id", 
    feedback: "NEGATIVE", 
    feedback_reason: "I don't like this memory because it is not relevant."
})
```

</CodeGroup>

## Feedback Types

The `feedback` parameter can be one of the following values:

- `POSITIVE`: The memory is useful.
- `NEGATIVE`: The memory is not useful.
- `VERY_NEGATIVE`: The memory is not useful at all.

## Parameters

The `feedback` method takes the following parameters:

- `memory_id`: The ID of the memory to give feedback on.
- `feedback`: The feedback to give on the memory. (Optional)
- `feedback_reason`: The reason for the feedback. (Optional)

The `feedback_reason` parameter is optional and can be used to provide a reason for the feedback.

<Note>
You can pass `None` or `null` to the `feedback` and `feedback_reason` parameters to remove the feedback for a memory.
</Note>

