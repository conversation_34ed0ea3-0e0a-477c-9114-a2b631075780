# OpenMemory MCP Server - Complete Setup Guide

This guide provides everything you need to run OpenMemory MCP server continuously and integrate it seamlessly with Augment for automatic memory storage.

## 🚀 Quick Start

### 1. One-Command Setup
```bash
# Make scripts executable
chmod +x *.sh

# Set up Augment integration
./setup-augment-integration.sh

# Start OpenMemory server
./start-openmemory.sh start

# Install as system service (optional, for auto-start)
./install-service.sh install
```

### 2. Verify Setup
```bash
# Check server status
./start-openmemory.sh status

# Test memory operations
python3 augment-tools/memory-helper.py add "I prefer TypeScript over JavaScript" preferences
python3 augment-tools/memory-helper.py search "TypeScript"
```

## 📋 Prerequisites

- **Python 3.8+** (required)
- **OpenAI API Key** (required)
- **Docker** (optional, for containerized deployment)
- **Node.js** (optional, for MCP client tools)

## 🛠 Detailed Setup

### Step 1: Environment Configuration

1. **Set OpenAI API Key**:
   ```bash
   # Check if already set
   cat openmemory/api/.env
   
   # If not set, add your key
   echo "OPENAI_API_KEY=your_api_key_here" > openmemory/api/.env
   ```

2. **Verify Prerequisites**:
   ```bash
   ./start-openmemory.sh status
   ```

### Step 2: Start OpenMemory Server

```bash
# Start the server
./start-openmemory.sh start

# Check if running
./start-openmemory.sh status

# View logs
./start-openmemory.sh logs
```

### Step 3: Configure Augment Integration

```bash
# Run the integration setup
./setup-augment-integration.sh

# This will:
# - Update MCP configuration with your username
# - Create memory helper tools
# - Set up Augment configuration files
# - Test the integration
```

### Step 4: Install as System Service (Optional)

```bash
# Install service for auto-start
./install-service.sh install

# Check service status
./install-service.sh status

# Uninstall if needed
./install-service.sh uninstall
```

## 🔧 Server Management

### Basic Commands

```bash
# Start server
./start-openmemory.sh start

# Stop server
./start-openmemory.sh stop

# Restart server
./start-openmemory.sh restart

# Check status
./start-openmemory.sh status

# View logs
./start-openmemory.sh logs
```

### Service Management (if installed)

**macOS (launchd):**
```bash
# Manual control
launchctl start com.openmemory.mcp
launchctl stop com.openmemory.mcp

# Check status
launchctl list | grep openmemory
```

**Linux (systemd):**
```bash
# Manual control
systemctl --user start openmemory-mcp
systemctl --user stop openmemory-mcp

# Check status
systemctl --user status openmemory-mcp
```

## 🧠 Memory Operations

### Using the Memory Helper

```bash
# Add memories with categories
python3 augment-tools/memory-helper.py add "I prefer clean, readable code" preferences
python3 augment-tools/memory-helper.py add "This project uses React with TypeScript" project
python3 augment-tools/memory-helper.py add "Fixed the async bug by adding await" solution

# Search memories
python3 augment-tools/memory-helper.py search "TypeScript"
python3 augment-tools/memory-helper.py search "bug fix" 3

# Get relevant context
python3 augment-tools/memory-helper.py context "React project"

# List all memories
python3 augment-tools/memory-helper.py list
```

### Memory Categories

- **preferences**: Coding style, personal choices, preferences
- **project**: Project-specific context, architecture, decisions
- **solution**: Problem-solving approaches, bug fixes, debugging
- **pattern**: Code patterns, best practices, reusable solutions

## 🔗 Augment Integration

### MCP Configuration

The integration creates an MCP configuration at:
- **File**: `augment-mcp-config.json`
- **Endpoint**: `http://localhost:8765/mcp/cursor/sse/YOUR_USERNAME`

### Automatic Memory Storage

Augment can automatically store memories when you:
- Express preferences ("I prefer...", "I like...", "I always...")
- Discuss project context ("This project...", "The architecture...")
- Solve problems ("The solution is...", "Fixed by...")
- Share patterns ("Best practice is...", "Pattern for...")

### Manual Memory Storage

```bash
# From command line
python3 augment-tools/memory-helper.py add "Remember to use error boundaries in React" pattern

# From within Augment (using MCP tools)
# Use the add_memories tool with your content
```

## 📊 Monitoring and Logs

### Log Files

- **Server logs**: `logs/openmemory.log`
- **Error logs**: `logs/openmemory_error.log`
- **Service logs**: `logs/service.log` (if using system service)

### Health Checks

```bash
# Check if server is responding
curl http://localhost:8765/docs

# Check MCP endpoint
curl http://localhost:8765/mcp/cursor/sse/$(whoami)

# Check frontend
curl http://localhost:3000
```

## 🌐 Access Points

Once running, you can access:

- **API Server**: http://localhost:8765
- **API Documentation**: http://localhost:8765/docs
- **Frontend Dashboard**: http://localhost:3000
- **MCP Endpoint**: http://localhost:8765/mcp/cursor/sse/YOUR_USERNAME

## 🔧 Troubleshooting

### Common Issues

1. **Server won't start**:
   ```bash
   # Check logs
   ./start-openmemory.sh logs
   
   # Verify OpenAI API key
   cat openmemory/api/.env
   
   # Check port availability
   lsof -i :8765
   ```

2. **Memory operations fail**:
   ```bash
   # Test server connectivity
   curl http://localhost:8765/docs
   
   # Check memory helper
   python3 augment-tools/memory-helper.py list
   ```

3. **Service won't auto-start**:
   ```bash
   # Check service status
   ./install-service.sh status
   
   # Reinstall service
   ./install-service.sh uninstall
   ./install-service.sh install
   ```

### Reset Everything

```bash
# Stop all services
./start-openmemory.sh stop
./install-service.sh uninstall

# Clean up
rm -rf logs/
rm -f augment-mcp-config.json

# Start fresh
./setup-augment-integration.sh
./start-openmemory.sh start
```

## 🔒 Privacy and Security

- **Local Only**: All data stays on your machine
- **No Cloud Sync**: Nothing is sent to external servers (except OpenAI API calls)
- **Encrypted Storage**: Vector embeddings are stored locally
- **Access Control**: Only accessible via localhost

## 📝 Configuration Files

- `augment-mcp-config.json` - MCP server configuration for Augment
- `openmemory/api/.env` - Environment variables (API keys)
- `augment-tools/memory-helper.py` - Memory management utilities
- `logs/` - All log files

## 🤝 Integration with Other Tools

The MCP endpoint works with any MCP-compatible client:
- **Cursor**: Use the MCP endpoint directly
- **Claude Desktop**: Add to MCP servers configuration
- **Windsurf**: Configure as MCP server
- **Custom Tools**: Use the REST API at http://localhost:8765

## 📚 Next Steps

1. **Start using memories**: Begin storing your preferences and project context
2. **Explore the dashboard**: Visit http://localhost:3000 to manage memories
3. **Integrate with workflows**: Use the memory helper in your development scripts
4. **Share across tools**: Configure other MCP clients to use the same endpoint

---

**Need help?** Check the logs, verify the server is running, and ensure your OpenAI API key is properly configured.
