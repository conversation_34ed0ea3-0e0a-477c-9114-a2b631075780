#!/bin/bash

# Simple memory storage script for Augment
# Usage: ./remember.sh "content to remember" [category]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INTEGRATION_SCRIPT="$SCRIPT_DIR/augment-memory-integration.py"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

# Check if content is provided
if [ $# -eq 0 ]; then
    echo "💭 Remember - Store memories for Augment"
    echo ""
    echo "Usage:"
    echo "  ./remember.sh \"content to remember\" [category]"
    echo ""
    echo "Examples:"
    echo "  ./remember.sh \"I prefer TypeScript over JavaScript\" preferences"
    echo "  ./remember.sh \"This project uses React with Next.js\" project"
    echo "  ./remember.sh \"Fixed the async bug by adding proper error handling\" solution"
    echo ""
    echo "Categories: preferences, project, solution, pattern, decision, general"
    exit 1
fi

# Get content and optional category
CONTENT="$1"
CATEGORY="${2:-auto}"

# Check if OpenMemory server is running
if ! python3 "$INTEGRATION_SCRIPT" status | grep -q "running"; then
    print_warning "OpenMemory server is not running. Starting it..."
    "$SCRIPT_DIR/start-openmemory.sh" start
    sleep 3
    
    # Check again
    if ! python3 "$INTEGRATION_SCRIPT" status | grep -q "running"; then
        print_error "Failed to start OpenMemory server"
        print_error "Please run: ./start-openmemory.sh start"
        exit 1
    fi
fi

# Store the memory
if [ "$CATEGORY" = "auto" ]; then
    # Let the integration script auto-categorize
    result=$(python3 "$INTEGRATION_SCRIPT" store "$CONTENT" 2>&1)
else
    # Use specified category
    result=$(python3 "$INTEGRATION_SCRIPT" store "$CONTENT" "$CATEGORY" 2>&1)
fi

# Check result
if echo "$result" | grep -q "successfully"; then
    print_success "Memory stored: $CONTENT"
    if echo "$result" | grep -q "category:"; then
        category=$(echo "$result" | grep -o "category: [a-z]*" | cut -d' ' -f2)
        echo "📂 Category: $category"
    fi
else
    print_error "Failed to store memory"
    echo "$result"
    exit 1
fi
