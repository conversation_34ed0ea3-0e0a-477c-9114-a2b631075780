#!/bin/bash

# OpenMemory MCP Server Startup Script
# This script provides an easy way to start the OpenMemory MCP server
# and ensures it runs continuously with proper monitoring

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OPENMEMORY_DIR="$SCRIPT_DIR/openmemory"
LOG_DIR="$SCRIPT_DIR/logs"
PID_FILE="$LOG_DIR/openmemory.pid"
LOG_FILE="$LOG_DIR/openmemory.log"
ERROR_LOG="$LOG_DIR/openmemory_error.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create logs directory
mkdir -p "$LOG_DIR"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[OPENMEMORY]${NC} $1"
}

# Function to check if OpenMemory is running
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Function to get the status
get_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        print_status "OpenMemory MCP Server is running (PID: $pid)"
        print_status "API Server: http://localhost:8765"
        print_status "Frontend: http://localhost:3000"
        print_status "MCP Endpoint: http://localhost:8765/mcp/cursor/sse/$(whoami)"
        return 0
    else
        print_warning "OpenMemory MCP Server is not running"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -d "$OPENMEMORY_DIR" ]; then
        print_error "OpenMemory directory not found at $OPENMEMORY_DIR"
        print_error "Please run this script from the OpenMemory-General root directory"
        exit 1
    fi
    
    # Check for OpenAI API key
    if [ ! -f "$OPENMEMORY_DIR/api/.env" ]; then
        print_error "Environment file not found. Creating from example..."
        if [ -f "$OPENMEMORY_DIR/api/.env.example" ]; then
            cp "$OPENMEMORY_DIR/api/.env.example" "$OPENMEMORY_DIR/api/.env"
            print_warning "Please edit $OPENMEMORY_DIR/api/.env and add your OPENAI_API_KEY"
            exit 1
        else
            print_error "No .env.example file found. Please create $OPENMEMORY_DIR/api/.env with OPENAI_API_KEY"
            exit 1
        fi
    fi
    
    # Check if OpenAI API key is set
    if ! grep -q "OPENAI_API_KEY=sk-" "$OPENMEMORY_DIR/api/.env" 2>/dev/null; then
        print_error "OPENAI_API_KEY not properly set in $OPENMEMORY_DIR/api/.env"
        print_error "Please add your OpenAI API key to the .env file"
        exit 1
    fi
    
    # Check for Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check for Docker (optional)
    if command -v docker &> /dev/null; then
        print_status "Docker found - can use Docker mode"
        DOCKER_AVAILABLE=true
    else
        print_warning "Docker not found - will use local Python mode"
        DOCKER_AVAILABLE=false
    fi
    
    print_status "Prerequisites check completed"
}

# Function to start the server
start_server() {
    if is_running; then
        print_warning "OpenMemory MCP Server is already running"
        get_status
        return 0
    fi
    
    print_header "Starting OpenMemory MCP Server..."
    
    # Change to openmemory directory
    cd "$OPENMEMORY_DIR"
    
    # Start the server based on available tools
    if [ "$DOCKER_AVAILABLE" = true ] && [ -f "docker-compose.yml" ]; then
        print_status "Starting with Docker Compose..."
        # Start in background and capture PID
        nohup make up > "$LOG_FILE" 2> "$ERROR_LOG" &
        local server_pid=$!
        echo $server_pid > "$PID_FILE"
        
        # Wait a moment for startup
        sleep 5
        
        # Check if it's actually running
        if is_running; then
            print_status "OpenMemory MCP Server started successfully!"
            get_status
        else
            print_error "Failed to start OpenMemory MCP Server"
            print_error "Check logs at $LOG_FILE and $ERROR_LOG"
            return 1
        fi
    else
        print_status "Starting with local Python..."
        # Install dependencies if needed
        if [ ! -d "api/venv" ]; then
            print_status "Creating virtual environment..."
            cd api
            python3 -m venv venv
            source venv/bin/activate
            pip install -r requirements.txt
            cd ..
        fi
        
        # Start the API server
        cd api
        source venv/bin/activate
        nohup uvicorn main:app --host 0.0.0.0 --port 8765 > "$LOG_FILE" 2> "$ERROR_LOG" &
        local server_pid=$!
        echo $server_pid > "$PID_FILE"
        cd ..
        
        # Wait a moment for startup
        sleep 3
        
        if is_running; then
            print_status "OpenMemory MCP Server started successfully!"
            get_status
        else
            print_error "Failed to start OpenMemory MCP Server"
            print_error "Check logs at $LOG_FILE and $ERROR_LOG"
            return 1
        fi
    fi
}

# Function to stop the server
stop_server() {
    if ! is_running; then
        print_warning "OpenMemory MCP Server is not running"
        return 0
    fi
    
    print_header "Stopping OpenMemory MCP Server..."
    
    local pid=$(cat "$PID_FILE")
    
    # Try graceful shutdown first
    kill "$pid" 2>/dev/null || true
    
    # Wait for graceful shutdown
    local count=0
    while [ $count -lt 10 ] && ps -p "$pid" > /dev/null 2>&1; do
        sleep 1
        count=$((count + 1))
    done
    
    # Force kill if still running
    if ps -p "$pid" > /dev/null 2>&1; then
        print_warning "Forcing shutdown..."
        kill -9 "$pid" 2>/dev/null || true
    fi
    
    # Clean up Docker if it was used
    if [ "$DOCKER_AVAILABLE" = true ]; then
        cd "$OPENMEMORY_DIR"
        docker compose down 2>/dev/null || true
    fi
    
    rm -f "$PID_FILE"
    print_status "OpenMemory MCP Server stopped"
}

# Function to restart the server
restart_server() {
    print_header "Restarting OpenMemory MCP Server..."
    stop_server
    sleep 2
    start_server
}

# Function to show logs
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        echo "=== OpenMemory Logs ==="
        tail -f "$LOG_FILE"
    else
        print_warning "No log file found at $LOG_FILE"
    fi
}

# Function to show help
show_help() {
    echo "OpenMemory MCP Server Management Script"
    echo ""
    echo "Usage: $0 {start|stop|restart|status|logs|help}"
    echo ""
    echo "Commands:"
    echo "  start    - Start the OpenMemory MCP Server"
    echo "  stop     - Stop the OpenMemory MCP Server"
    echo "  restart  - Restart the OpenMemory MCP Server"
    echo "  status   - Show server status"
    echo "  logs     - Show server logs (tail -f)"
    echo "  help     - Show this help message"
    echo ""
    echo "The server will be available at:"
    echo "  API Server: http://localhost:8765"
    echo "  Frontend: http://localhost:3000"
    echo "  MCP Endpoint: http://localhost:8765/mcp/cursor/sse/$(whoami)"
}

# Main script logic
case "${1:-}" in
    start)
        check_prerequisites
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        check_prerequisites
        restart_server
        ;;
    status)
        get_status
        ;;
    logs)
        show_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Invalid command: ${1:-}"
        echo ""
        show_help
        exit 1
        ;;
esac
