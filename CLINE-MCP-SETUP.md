# Cline MCP Configuration for OpenMemory

## 🔧 **Fix Your Cline Configuration**

### **Current Issue:**
Your Cline is configured with: `http://localhost:8765`
This is incomplete and won't work.

### **✅ Correct Configuration:**

1. **Open Cline Settings** (click the gear ⚙️ icon in Cline)

2. **Find MCP Servers section**

3. **Use this EXACT configuration:**

```json
{
  "mcpServers": {
    "openmemory": {
      "command": "node",
      "args": ["-e", "console.log('MCP Server Ready')"],
      "env": {},
      "transport": {
        "type": "sse",
        "url": "http://localhost:8765/mcp/cline/sse/deepakbatham"
      }
    }
  }
}
```

**OR** if your Cline uses a simpler format:

```
MCP Server URL: http://localhost:8765/mcp/cline/sse/deepakbatham
```

### **🎯 Key Points:**

- ✅ **Full Path Required**: Must include `/mcp/cline/sse/deepakbatham`
- ✅ **Your Username**: `deepakbatham` is your user ID
- ✅ **Client Name**: `cline` identifies the client type
- ✅ **SSE Protocol**: Server-Sent Events for real-time communication

### **🧪 Test the Configuration:**

After updating Cline settings:

1. **Restart Cline** (close and reopen the extension)
2. **Test memory storage** by saying:
   ```
   "I prefer TypeScript over JavaScript for type safety"
   ```
3. **Check if it worked** by asking:
   ```
   "What programming language do I prefer?"
   ```

### **🔍 Verify It's Working:**

You should see:
- ✅ Cline can access your previous memories
- ✅ New preferences get stored automatically
- ✅ Context from past conversations is available

### **🚨 Troubleshooting:**

If it still doesn't work:

1. **Check OpenMemory is running:**
   ```bash
   curl http://localhost:8765/docs
   ```

2. **Test MCP endpoint directly:**
   ```bash
   curl http://localhost:8765/mcp/cline/sse/deepakbatham
   ```

3. **Check Cline logs** for error messages

4. **Try restarting both:**
   - Restart OpenMemory: `./start-openmemory.sh restart`
   - Restart Cline extension

### **📊 Available MCP Tools:**

Once configured, Cline will have access to:
- `add_memories` - Store new memories
- `search_memory` - Search existing memories  
- `list_memories` - List all memories
- `delete_all_memories` - Clear all memories

### **🎉 Success Indicators:**

When working correctly:
- Cline remembers your preferences across sessions
- You can ask "What do you know about me?" and get relevant info
- New important information gets automatically stored
- Context from previous conversations is maintained

---

**Need help?** Run `./start-openmemory.sh status` to check if OpenMemory is running properly.
