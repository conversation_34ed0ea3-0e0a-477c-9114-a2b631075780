#!/bin/bash

# Augment-OpenMemory Integration Setup Script
# This script configures OpenMemory to work seamlessly with Augment

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
USER=$(whoami)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Function to update the MCP config with the correct username
update_mcp_config() {
    print_header "Updating MCP configuration for user: $USER"
    
    local config_file="$SCRIPT_DIR/augment-mcp-config.json"
    
    if [ -f "$config_file" ]; then
        # Replace YOUR_USERNAME with actual username
        sed -i.bak "s/YOUR_USERNAME/$USER/g" "$config_file"
        rm -f "$config_file.bak"
        print_status "MCP configuration updated with username: $USER"
    else
        print_error "MCP configuration file not found: $config_file"
        return 1
    fi
}

# Function to create Augment-specific memory tools
create_augment_tools() {
    print_header "Creating Augment integration tools..."
    
    # Create a directory for Augment tools
    mkdir -p "$SCRIPT_DIR/augment-tools"
    
    # Create memory helper script
    cat > "$SCRIPT_DIR/augment-tools/memory-helper.py" << 'EOF'
#!/usr/bin/env python3
"""
Augment Memory Helper
Provides utilities for automatic memory storage and retrieval with OpenMemory
"""

import requests
import json
import sys
import os
from typing import List, Dict, Any, Optional

class OpenMemoryClient:
    def __init__(self, base_url: str = "http://localhost:8765", user_id: str = None):
        self.base_url = base_url
        self.user_id = user_id or os.getenv('USER', 'default')
        
    def add_memory(self, content: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Add a memory to OpenMemory"""
        url = f"{self.base_url}/memories"
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": content
                }
            ],
            "user_id": self.user_id,
            "metadata": metadata or {}
        }
        
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error adding memory: {e}", file=sys.stderr)
            return {"error": str(e)}
    
    def search_memories(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search memories in OpenMemory"""
        url = f"{self.base_url}/search"
        
        payload = {
            "query": query,
            "user_id": self.user_id
        }
        
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error searching memories: {e}", file=sys.stderr)
            return []
    
    def get_all_memories(self) -> List[Dict[str, Any]]:
        """Get all memories for the user"""
        url = f"{self.base_url}/memories"
        
        params = {"user_id": self.user_id}
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error getting memories: {e}", file=sys.stderr)
            return []

def auto_store_memory(content: str, category: str = "general") -> bool:
    """Automatically store memory with categorization"""
    client = OpenMemoryClient()
    
    # Define memory categories and their triggers
    categories = {
        "preferences": ["prefer", "like", "dislike", "always", "never", "style"],
        "project": ["project", "architecture", "design", "requirement"],
        "solution": ["solution", "fix", "bug", "error", "problem", "debug"],
        "pattern": ["pattern", "best practice", "approach", "method"]
    }
    
    # Auto-detect category if not specified
    if category == "general":
        content_lower = content.lower()
        for cat, keywords in categories.items():
            if any(keyword in content_lower for keyword in keywords):
                category = cat
                break
    
    metadata = {
        "category": category,
        "source": "augment_auto",
        "timestamp": str(int(time.time()))
    }
    
    result = client.add_memory(content, metadata)
    return "error" not in result

def get_relevant_context(query: str, max_memories: int = 3) -> str:
    """Get relevant context for a query"""
    client = OpenMemoryClient()
    memories = client.search_memories(query, limit=max_memories)
    
    if not memories:
        return ""
    
    context_parts = []
    for memory in memories:
        if isinstance(memory, dict) and "memory" in memory:
            context_parts.append(f"- {memory['memory']}")
    
    return "\n".join(context_parts)

if __name__ == "__main__":
    import time
    
    if len(sys.argv) < 2:
        print("Usage: memory-helper.py <command> [args...]")
        print("Commands:")
        print("  add <content> [category]     - Add a memory")
        print("  search <query> [limit]       - Search memories")
        print("  context <query> [max]        - Get relevant context")
        print("  list                         - List all memories")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "add":
        if len(sys.argv) < 3:
            print("Error: Content required for add command")
            sys.exit(1)
        content = sys.argv[2]
        category = sys.argv[3] if len(sys.argv) > 3 else "general"
        success = auto_store_memory(content, category)
        print("Memory stored successfully" if success else "Failed to store memory")
    
    elif command == "search":
        if len(sys.argv) < 3:
            print("Error: Query required for search command")
            sys.exit(1)
        query = sys.argv[2]
        limit = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        client = OpenMemoryClient()
        memories = client.search_memories(query, limit)
        print(json.dumps(memories, indent=2))
    
    elif command == "context":
        if len(sys.argv) < 3:
            print("Error: Query required for context command")
            sys.exit(1)
        query = sys.argv[2]
        max_memories = int(sys.argv[3]) if len(sys.argv) > 3 else 3
        context = get_relevant_context(query, max_memories)
        print(context)
    
    elif command == "list":
        client = OpenMemoryClient()
        memories = client.get_all_memories()
        print(json.dumps(memories, indent=2))
    
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
EOF

    chmod +x "$SCRIPT_DIR/augment-tools/memory-helper.py"
    print_status "Memory helper tool created"
}

# Function to create Augment configuration
create_augment_config() {
    print_header "Creating Augment configuration..."
    
    local augment_config_dir="$HOME/.config/augment"
    mkdir -p "$augment_config_dir"
    
    # Copy the MCP configuration
    cp "$SCRIPT_DIR/augment-mcp-config.json" "$augment_config_dir/openmemory-mcp.json"
    
    # Create a simple integration script
    cat > "$augment_config_dir/openmemory-integration.sh" << EOF
#!/bin/bash
# OpenMemory Integration for Augment
# This script provides easy access to OpenMemory functions

MEMORY_HELPER="$SCRIPT_DIR/augment-tools/memory-helper.py"

# Function to store memory automatically
store_memory() {
    local content="\$1"
    local category="\${2:-general}"
    python3 "\$MEMORY_HELPER" add "\$content" "\$category"
}

# Function to get relevant context
get_context() {
    local query="\$1"
    local max_memories="\${2:-3}"
    python3 "\$MEMORY_HELPER" context "\$query" "\$max_memories"
}

# Function to search memories
search_memories() {
    local query="\$1"
    local limit="\${2:-5}"
    python3 "\$MEMORY_HELPER" search "\$query" "\$limit"
}

# Export functions for use in other scripts
export -f store_memory get_context search_memories
EOF

    chmod +x "$augment_config_dir/openmemory-integration.sh"
    print_status "Augment configuration created at $augment_config_dir"
}

# Function to test the integration
test_integration() {
    print_header "Testing OpenMemory integration..."
    
    # Check if OpenMemory server is running
    if curl -s "http://localhost:8765/docs" > /dev/null; then
        print_status "OpenMemory server is running"
    else
        print_warning "OpenMemory server is not running. Starting it..."
        "$SCRIPT_DIR/start-openmemory.sh" start
        sleep 5
    fi
    
    # Test memory operations
    print_status "Testing memory operations..."
    
    # Test adding a memory
    python3 "$SCRIPT_DIR/augment-tools/memory-helper.py" add "Test memory from Augment integration setup" "test" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        print_status "Memory storage test: PASSED"
    else
        print_warning "Memory storage test: FAILED"
    fi
    
    # Test searching memories
    result=$(python3 "$SCRIPT_DIR/augment-tools/memory-helper.py" search "test" 1 2>/dev/null)
    
    if [ -n "$result" ] && [ "$result" != "[]" ]; then
        print_status "Memory search test: PASSED"
    else
        print_warning "Memory search test: FAILED"
    fi
    
    print_status "Integration testing completed"
}

# Function to show usage instructions
show_usage() {
    print_header "Augment-OpenMemory Integration Setup Complete!"
    echo ""
    print_status "Configuration files created:"
    print_status "  • MCP Config: $SCRIPT_DIR/augment-mcp-config.json"
    print_status "  • Augment Config: $HOME/.config/augment/openmemory-mcp.json"
    print_status "  • Memory Helper: $SCRIPT_DIR/augment-tools/memory-helper.py"
    echo ""
    print_status "OpenMemory endpoints:"
    print_status "  • API Server: http://localhost:8765"
    print_status "  • Frontend: http://localhost:3000"
    print_status "  • MCP Endpoint: http://localhost:8765/mcp/cursor/sse/$USER"
    echo ""
    print_status "To use with Augment:"
    print_status "  1. Make sure OpenMemory is running: ./start-openmemory.sh start"
    print_status "  2. Configure Augment to use the MCP endpoint above"
    print_status "  3. Use the memory helper tools for automatic memory management"
    echo ""
    print_status "Memory Helper Usage:"
    print_status "  • Store memory: python3 augment-tools/memory-helper.py add 'content' [category]"
    print_status "  • Search: python3 augment-tools/memory-helper.py search 'query'"
    print_status "  • Get context: python3 augment-tools/memory-helper.py context 'query'"
    echo ""
    print_warning "Next steps:"
    print_warning "  1. Start OpenMemory if not running: ./start-openmemory.sh start"
    print_warning "  2. Install as service for auto-start: ./install-service.sh install"
    print_warning "  3. Configure Augment to use the MCP endpoint"
}

# Main setup function
main() {
    print_header "Setting up Augment-OpenMemory Integration..."
    
    # Update MCP configuration
    update_mcp_config
    
    # Create Augment tools
    create_augment_tools
    
    # Create Augment configuration
    create_augment_config
    
    # Test the integration
    test_integration
    
    # Show usage instructions
    show_usage
}

# Run main setup
main
