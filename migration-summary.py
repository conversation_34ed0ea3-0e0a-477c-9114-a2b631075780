#!/usr/bin/env python3
"""
Migration Summary - Show what was migrated from Augment to OpenMemory
"""

import requests
import json
from datetime import datetime

# Configuration
OPENMEMORY_API_URL = "http://localhost:8765"
USER_ID = "deepakbatham"

def get_migrated_memories():
    """Get all memories that were migrated from Augment"""
    url = f"{OPENMEMORY_API_URL}/api/v1/memories/"
    
    try:
        response = requests.get(url, params={"user_id": USER_ID, "size": 100}, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'items' in data:
                return data['items']
            return data if isinstance(data, list) else []
        else:
            print(f"❌ Error fetching memories: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def categorize_memories(memories):
    """Categorize memories by source"""
    categories = {
        "augment_migration": [],
        "augment_extraction": [],
        "test_memories": [],
        "other": []
    }
    
    for memory in memories:
        metadata = memory.get('metadata_', {})
        source = metadata.get('source', 'unknown')
        
        if source == "augment_migration":
            categories["augment_migration"].append(memory)
        elif source == "augment_extraction":
            categories["augment_extraction"].append(memory)
        elif source == "augment" or metadata.get('category') == 'test':
            categories["test_memories"].append(memory)
        else:
            categories["other"].append(memory)
    
    return categories

def print_memory_summary(memory, index):
    """Print a summary of a single memory"""
    content = memory.get('content', 'No content')
    metadata = memory.get('metadata_', {})
    created_at = memory.get('created_at')
    
    # Truncate content for display
    content_preview = content[:100] + "..." if len(content) > 100 else content
    
    print(f"  {index}. {content_preview}")
    print(f"     📅 Created: {created_at}")
    if metadata.get('entity_name'):
        print(f"     🏷️  Entity: {metadata['entity_name']} ({metadata.get('entity_type', 'unknown')})")
    if metadata.get('category'):
        print(f"     📂 Category: {metadata['category']}")
    print()

def main():
    print("📊 Augment to OpenMemory Migration Summary")
    print("=" * 60)
    
    # Check connection
    try:
        response = requests.get(f"{OPENMEMORY_API_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ Cannot connect to OpenMemory API")
            return False
    except:
        print("❌ Cannot connect to OpenMemory API")
        return False
    
    print("✅ Connected to OpenMemory API")
    
    # Get all memories
    memories = get_migrated_memories()
    
    if not memories:
        print("⚠️  No memories found")
        return False
    
    # Categorize memories
    categories = categorize_memories(memories)
    
    total_memories = len(memories)
    print(f"\n📈 Total memories in OpenMemory: {total_memories}")
    
    # Show migration summary
    print(f"\n🧠 Augment Migration Memories: {len(categories['augment_migration'])}")
    if categories['augment_migration']:
        for i, memory in enumerate(categories['augment_migration'], 1):
            print_memory_summary(memory, i)
    
    print(f"🔍 Augment Extraction Memories: {len(categories['augment_extraction'])}")
    if categories['augment_extraction']:
        for i, memory in enumerate(categories['augment_extraction'], 1):
            print_memory_summary(memory, i)
    
    print(f"🧪 Test Memories: {len(categories['test_memories'])}")
    if categories['test_memories']:
        for i, memory in enumerate(categories['test_memories'], 1):
            print_memory_summary(memory, i)
    
    print(f"📝 Other Memories: {len(categories['other'])}")
    if categories['other']:
        for i, memory in enumerate(categories['other'], 1):
            print_memory_summary(memory, i)
    
    # Show access information
    print("=" * 60)
    print("🔗 Access Information:")
    print(f"🌐 OpenMemory UI: http://localhost:3000")
    print(f"🔧 API Endpoint: {OPENMEMORY_API_URL}")
    print(f"🤖 MCP Endpoint: {OPENMEMORY_API_URL}/mcp/cursor/sse/{USER_ID}")
    
    print("\n✅ Migration Status: COMPLETE")
    print("💡 All your Augment memories are now available in OpenMemory!")
    print("🚀 Configure Augment to use the MCP endpoint for future memories.")
    
    return True

if __name__ == "__main__":
    main()
