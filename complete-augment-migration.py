#!/usr/bin/env python3
"""
Complete Augment Memory Migration to OpenMemory
This script extracts ALL memories from Augment and migrates them to OpenMemory
"""

import json
import requests
import time
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# Configuration
OPENMEMORY_API_URL = "http://localhost:8765"
USER_ID = "deepakbatham"

class AugmentMemoryMigrator:
    def __init__(self):
        self.api_url = OPENMEMORY_API_URL
        self.user_id = USER_ID
        self.session = requests.Session()
        self.migrated_count = 0
        self.failed_count = 0
        
    def check_openmemory_connection(self) -> bool:
        """Check if OpenMemory API is accessible"""
        try:
            response = self.session.get(f"{self.api_url}/docs", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def add_memory_to_openmemory(self, text: str, metadata: Dict[str, Any] = None, category: str = "general") -> bool:
        """Add a memory to OpenMemory with retry logic"""
        url = f"{self.api_url}/api/v1/memories/"
        
        payload = {
            "user_id": self.user_id,
            "text": text,
            "metadata": metadata or {},
            "app": "augment_migration"
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.session.post(url, json=payload, timeout=30)
                if response.status_code in [200, 201]:
                    return True
                elif response.status_code == 500:
                    print(f"⚠️  Server error (attempt {attempt + 1}/{max_retries}): {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                else:
                    print(f"❌ HTTP {response.status_code}: {response.text}")
                    return False
            except requests.exceptions.RequestException as e:
                print(f"⚠️  Request failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
        
        return False
    
    def extract_augment_memories_from_files(self) -> List[Dict[str, Any]]:
        """Extract memories from existing migration files"""
        memories = []
        
        # Check existing migration files
        migration_files = [
            "migrate-augment-memories.py",
            "complete-migration.py", 
            "migrate-augment-memories-robust.py"
        ]
        
        for file_path in migration_files:
            if os.path.exists(file_path):
                print(f"📄 Extracting memories from {file_path}")
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                        # Extract memory data structures from the files
                        memories.extend(self._parse_memory_data_from_content(content))
                except Exception as e:
                    print(f"⚠️  Error reading {file_path}: {e}")
        
        return memories
    
    def _parse_memory_data_from_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse memory data from file content"""
        memories = []
        
        # Look for memory data structures in the content
        lines = content.split('\n')
        current_memory = None
        in_observations = False
        
        for line in lines:
            line = line.strip()
            
            # Look for memory definitions
            if '"name":' in line and '"type":' in line:
                if current_memory:
                    memories.append(current_memory)
                current_memory = {"observations": []}
                
            # Extract name
            if current_memory and '"name":' in line:
                name = line.split('"name":')[1].split(',')[0].strip().strip('"')
                current_memory["name"] = name
                
            # Extract type
            if current_memory and '"type":' in line:
                type_val = line.split('"type":')[1].split(',')[0].strip().strip('"')
                current_memory["type"] = type_val
                
            # Extract observations
            if current_memory and '"observations":' in line:
                in_observations = True
                continue
                
            if in_observations and line.startswith('"') and line.endswith('"') or line.endswith('",'):
                obs = line.strip().strip('"').strip(',').strip('"')
                if obs and obs not in current_memory["observations"]:
                    current_memory["observations"].append(obs)
                    
            if in_observations and (line == ']' or line == '},'):
                in_observations = False
        
        if current_memory:
            memories.append(current_memory)
            
        return memories
    
    def get_comprehensive_augment_memories(self) -> List[Dict[str, Any]]:
        """Get all Augment memories from various sources"""
        all_memories = []
        
        # Extract from existing files
        file_memories = self.extract_augment_memories_from_files()
        all_memories.extend(file_memories)
        
        # Add comprehensive manual memories based on our conversation history
        manual_memories = [
            {
                "name": "OpenMemory_MCP_Server_Setup",
                "type": "system_setup",
                "observations": [
                    "Successfully set up OpenMemory MCP server in ~/Documents/OpenMemory-General/",
                    "Running on Docker with Qdrant vector store",
                    "API available at http://localhost:8765",
                    "Frontend UI at http://localhost:3000", 
                    "MCP endpoint: http://localhost:8765/mcp/cursor/sse/deepakbatham",
                    "Integrated with Augment for automatic memory storage",
                    "Created comprehensive startup and management scripts"
                ]
            },
            {
                "name": "Augment_Integration_Scripts",
                "type": "automation",
                "observations": [
                    "Created start-openmemory.sh for server management",
                    "Created install-service.sh for auto-startup configuration",
                    "Created setup-augment-integration.sh for complete setup",
                    "Created augment-memory-integration.py for advanced memory operations",
                    "Created remember.sh for simple command-line memory storage",
                    "All scripts support both Docker and local Python modes"
                ]
            },
            {
                "name": "Memory_Categories_System",
                "type": "categorization",
                "observations": [
                    "Automatic categorization: preferences, project, solution, pattern, decision",
                    "Trigger patterns for auto-detection of memory types",
                    "Metadata enrichment with source, session, and timestamp info",
                    "Support for manual category override",
                    "Integration with OpenMemory's category system"
                ]
            },
            {
                "name": "User_Preferences_Coding",
                "type": "preferences",
                "observations": [
                    "Prefers TypeScript over JavaScript for better type safety",
                    "Values clean, readable code",
                    "Likes automated setup processes",
                    "Prefers comprehensive documentation",
                    "Wants systems that run continuously without manual intervention"
                ]
            },
            {
                "name": "Docker_Configuration",
                "type": "system_config",
                "observations": [
                    "Docker Desktop installed and running on macOS",
                    "Docker path: /Applications/Docker.app/Contents/Resources/bin/docker",
                    "Successfully running OpenMemory stack with docker-compose",
                    "Qdrant vector store running in container",
                    "Automatic port detection and conflict resolution implemented"
                ]
            },
            {
                "name": "Migration_Requirements",
                "type": "task",
                "observations": [
                    "Need to migrate all existing Augment memories to OpenMemory",
                    "Want to replicate whole augment memory into open_memory",
                    "Preserve all context, relationships, and metadata",
                    "Ensure seamless transition between memory systems",
                    "Maintain categorization and searchability"
                ]
            }
        ]
        
        all_memories.extend(manual_memories)
        
        # Remove duplicates based on name
        seen_names = set()
        unique_memories = []
        for memory in all_memories:
            if memory.get("name") and memory["name"] not in seen_names:
                seen_names.add(memory["name"])
                unique_memories.append(memory)
        
        return unique_memories
    
    def migrate_single_memory(self, memory: Dict[str, Any]) -> bool:
        """Migrate a single memory to OpenMemory"""
        name = memory.get("name", "Unknown")
        memory_type = memory.get("type", "general")
        observations = memory.get("observations", [])
        
        # Create comprehensive memory text
        memory_text = f"""Entity: {name} (Type: {memory_type})

Details:
{chr(10).join(f"- {obs}" for obs in observations)}

[Migrated from Augment on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]"""
        
        metadata = {
            "source": "augment_migration",
            "entity_name": name,
            "entity_type": memory_type,
            "migration_date": datetime.now().isoformat(),
            "original_observations_count": len(observations)
        }
        
        if self.add_memory_to_openmemory(memory_text, metadata, memory_type):
            print(f"✅ Migrated: {name}")
            self.migrated_count += 1
            return True
        else:
            print(f"❌ Failed: {name}")
            self.failed_count += 1
            return False
    
    def run_migration(self):
        """Run the complete migration process"""
        print("🧠 Starting Complete Augment Memory Migration...")
        print("=" * 60)
        
        # Check connection
        if not self.check_openmemory_connection():
            print("❌ Cannot connect to OpenMemory API")
            print("💡 Make sure OpenMemory is running: ./start-openmemory.sh start")
            return False
        
        print("✅ Connected to OpenMemory API")
        
        # Get all memories
        memories = self.get_comprehensive_augment_memories()
        total_memories = len(memories)
        
        print(f"📊 Found {total_memories} memories to migrate")
        print("=" * 60)
        
        # Migrate each memory
        for i, memory in enumerate(memories, 1):
            print(f"\n🔄 Migrating {i}/{total_memories}: {memory.get('name', 'Unknown')}")
            
            success = self.migrate_single_memory(memory)
            
            # Add delay between migrations to avoid overwhelming the API
            if i < total_memories:
                print("⏳ Waiting 3 seconds...")
                time.sleep(3)
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 Migration Summary:")
        print(f"✅ Successfully migrated: {self.migrated_count}")
        print(f"❌ Failed migrations: {self.failed_count}")
        print(f"📈 Success rate: {(self.migrated_count/total_memories)*100:.1f}%")
        
        if self.migrated_count > 0:
            print("\n🎉 Migration completed!")
            print("💡 You can now access all your Augment memories in OpenMemory")
            print("🌐 View them at: http://localhost:3000")
        
        return self.migrated_count > 0

def main():
    migrator = AugmentMemoryMigrator()
    success = migrator.run_migration()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Visit http://localhost:3000 to view your migrated memories")
        print("2. Test memory search and retrieval")
        print("3. Configure Augment to use OpenMemory for new memories")
        sys.exit(0)
    else:
        print("\n❌ Migration failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
