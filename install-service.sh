#!/bin/bash

# OpenMemory Service Installation Script
# This script sets up OpenMemory to run automatically on system startup

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_NAME="openmemory-mcp"
USER=$(whoami)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[INSTALL]${NC} $1"
}

# Detect the operating system
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Install for macOS using launchd
install_macos() {
    print_header "Installing OpenMemory service for macOS..."
    
    local plist_file="$HOME/Library/LaunchAgents/com.openmemory.mcp.plist"
    
    # Create the plist file
    cat > "$plist_file" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.openmemory.mcp</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>$SCRIPT_DIR/start-openmemory.sh</string>
        <string>start</string>
    </array>
    <key>WorkingDirectory</key>
    <string>$SCRIPT_DIR</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
    </dict>
    <key>StandardOutPath</key>
    <string>$SCRIPT_DIR/logs/service.log</string>
    <key>StandardErrorPath</key>
    <string>$SCRIPT_DIR/logs/service_error.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin</string>
    </dict>
</dict>
</plist>
EOF

    # Load the service
    launchctl unload "$plist_file" 2>/dev/null || true
    launchctl load "$plist_file"
    
    print_status "macOS service installed successfully!"
    print_status "Service will start automatically on login"
    print_status "To manually control the service:"
    print_status "  Start: launchctl start com.openmemory.mcp"
    print_status "  Stop:  launchctl stop com.openmemory.mcp"
    print_status "  Uninstall: launchctl unload $plist_file && rm $plist_file"
}

# Install for Linux using systemd
install_linux() {
    print_header "Installing OpenMemory service for Linux..."
    
    local service_file="$HOME/.config/systemd/user/$SERVICE_NAME.service"
    
    # Create systemd user directory if it doesn't exist
    mkdir -p "$HOME/.config/systemd/user"
    
    # Create the service file
    cat > "$service_file" << EOF
[Unit]
Description=OpenMemory MCP Server
After=network.target

[Service]
Type=forking
ExecStart=$SCRIPT_DIR/start-openmemory.sh start
ExecStop=$SCRIPT_DIR/start-openmemory.sh stop
ExecReload=$SCRIPT_DIR/start-openmemory.sh restart
WorkingDirectory=$SCRIPT_DIR
User=$USER
Restart=always
RestartSec=10
StandardOutput=append:$SCRIPT_DIR/logs/service.log
StandardError=append:$SCRIPT_DIR/logs/service_error.log

[Install]
WantedBy=default.target
EOF

    # Reload systemd and enable the service
    systemctl --user daemon-reload
    systemctl --user enable "$SERVICE_NAME"
    systemctl --user start "$SERVICE_NAME"
    
    print_status "Linux service installed successfully!"
    print_status "Service will start automatically on login"
    print_status "To manually control the service:"
    print_status "  Start: systemctl --user start $SERVICE_NAME"
    print_status "  Stop:  systemctl --user stop $SERVICE_NAME"
    print_status "  Status: systemctl --user status $SERVICE_NAME"
    print_status "  Uninstall: systemctl --user disable $SERVICE_NAME && rm $service_file"
}

# Uninstall service
uninstall_service() {
    print_header "Uninstalling OpenMemory service..."
    
    if [[ "$OS" == "macos" ]]; then
        local plist_file="$HOME/Library/LaunchAgents/com.openmemory.mcp.plist"
        if [ -f "$plist_file" ]; then
            launchctl unload "$plist_file" 2>/dev/null || true
            rm "$plist_file"
            print_status "macOS service uninstalled"
        else
            print_warning "macOS service not found"
        fi
    elif [[ "$OS" == "linux" ]]; then
        local service_file="$HOME/.config/systemd/user/$SERVICE_NAME.service"
        if [ -f "$service_file" ]; then
            systemctl --user stop "$SERVICE_NAME" 2>/dev/null || true
            systemctl --user disable "$SERVICE_NAME" 2>/dev/null || true
            rm "$service_file"
            systemctl --user daemon-reload
            print_status "Linux service uninstalled"
        else
            print_warning "Linux service not found"
        fi
    fi
}

# Check service status
check_status() {
    print_header "Checking service status..."
    
    if [[ "$OS" == "macos" ]]; then
        local plist_file="$HOME/Library/LaunchAgents/com.openmemory.mcp.plist"
        if [ -f "$plist_file" ]; then
            if launchctl list | grep -q "com.openmemory.mcp"; then
                print_status "macOS service is loaded and running"
            else
                print_warning "macOS service is installed but not loaded"
            fi
        else
            print_warning "macOS service is not installed"
        fi
    elif [[ "$OS" == "linux" ]]; then
        if systemctl --user is-enabled "$SERVICE_NAME" >/dev/null 2>&1; then
            local status=$(systemctl --user is-active "$SERVICE_NAME")
            if [ "$status" = "active" ]; then
                print_status "Linux service is enabled and running"
            else
                print_warning "Linux service is enabled but not running (status: $status)"
            fi
        else
            print_warning "Linux service is not installed or enabled"
        fi
    fi
}

# Show help
show_help() {
    echo "OpenMemory Service Installation Script"
    echo ""
    echo "Usage: $0 {install|uninstall|status|help}"
    echo ""
    echo "Commands:"
    echo "  install    - Install OpenMemory as a system service"
    echo "  uninstall  - Remove the OpenMemory service"
    echo "  status     - Check service installation status"
    echo "  help       - Show this help message"
    echo ""
    echo "After installation, OpenMemory will start automatically when you log in."
}

# Main script logic
detect_os

case "${1:-}" in
    install)
        # Make sure the start script is executable
        chmod +x "$SCRIPT_DIR/start-openmemory.sh"
        
        # Create logs directory
        mkdir -p "$SCRIPT_DIR/logs"
        
        if [[ "$OS" == "macos" ]]; then
            install_macos
        elif [[ "$OS" == "linux" ]]; then
            install_linux
        fi
        ;;
    uninstall)
        uninstall_service
        ;;
    status)
        check_status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Invalid command: ${1:-}"
        echo ""
        show_help
        exit 1
        ;;
esac
