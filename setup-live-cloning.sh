#!/bin/bash

# Setup Live Augment Memory Cloning
# This script configures automatic, continuous cloning of ALL Augment memories

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}[LIVE-CLONE]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

# Function to create systemd service for live monitoring
create_monitor_service() {
    print_header "Setting up live memory monitoring service..."
    
    local service_name="augment-memory-monitor"
    local service_file="$HOME/.config/systemd/user/$service_name.service"
    
    # Create systemd user directory if it doesn't exist
    mkdir -p "$HOME/.config/systemd/user"
    
    # Create the service file
    cat > "$service_file" << EOF
[Unit]
Description=Augment Memory Monitor - Live Cloning Service
After=network.target
Wants=openmemory-mcp.service

[Service]
Type=simple
ExecStart=$SCRIPT_DIR/augment-memory-monitor.py
WorkingDirectory=$SCRIPT_DIR
User=$(whoami)
Restart=always
RestartSec=30
StandardOutput=append:$SCRIPT_DIR/logs/monitor.log
StandardError=append:$SCRIPT_DIR/logs/monitor_error.log
Environment=PYTHONPATH=$SCRIPT_DIR

[Install]
WantedBy=default.target
EOF

    # Make the monitor script executable
    chmod +x "$SCRIPT_DIR/augment-memory-monitor.py"
    
    # Create logs directory
    mkdir -p "$SCRIPT_DIR/logs"
    
    print_success "Monitor service created at $service_file"
}

# Function to create macOS launchd service
create_macos_monitor_service() {
    print_header "Setting up macOS live memory monitoring service..."
    
    local plist_file="$HOME/Library/LaunchAgents/com.augment.memory.monitor.plist"
    
    # Create the plist file
    cat > "$plist_file" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.augment.memory.monitor</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/bin/python3</string>
        <string>$SCRIPT_DIR/augment-memory-monitor.py</string>
    </array>
    <key>WorkingDirectory</key>
    <string>$SCRIPT_DIR</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
    </dict>
    <key>StandardOutPath</key>
    <string>$SCRIPT_DIR/logs/monitor.log</string>
    <key>StandardErrorPath</key>
    <string>$SCRIPT_DIR/logs/monitor_error.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin</string>
        <key>PYTHONPATH</key>
        <string>$SCRIPT_DIR</string>
    </dict>
    <key>ThrottleInterval</key>
    <integer>30</integer>
</dict>
</plist>
EOF

    # Make the monitor script executable
    chmod +x "$SCRIPT_DIR/augment-memory-monitor.py"
    
    # Create logs directory
    mkdir -p "$SCRIPT_DIR/logs"
    
    print_success "macOS monitor service created at $plist_file"
}

# Function to install the monitoring service
install_monitor_service() {
    print_header "Installing live memory monitoring service..."
    
    # Detect OS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        create_macos_monitor_service
        
        # Load the service
        launchctl unload "$HOME/Library/LaunchAgents/com.augment.memory.monitor.plist" 2>/dev/null || true
        launchctl load "$HOME/Library/LaunchAgents/com.augment.memory.monitor.plist"
        
        print_success "macOS monitor service installed and started"
        print_success "Monitor will start automatically on login"
        
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        create_monitor_service
        
        # Reload systemd and enable the service
        systemctl --user daemon-reload
        systemctl --user enable augment-memory-monitor
        systemctl --user start augment-memory-monitor
        
        print_success "Linux monitor service installed and started"
        print_success "Monitor will start automatically on login"
    else
        print_error "Unsupported operating system: $OSTYPE"
        return 1
    fi
}

# Function to configure Augment for all projects
configure_augment_global() {
    print_header "Configuring global Augment MCP integration..."
    
    # Create global Augment configuration directory
    local augment_config_dir="$HOME/.config/augment"
    mkdir -p "$augment_config_dir"
    
    # Create global MCP configuration
    cat > "$augment_config_dir/mcp-servers.json" << EOF
{
  "mcpServers": {
    "openmemory": {
      "command": "python3",
      "args": [
        "$SCRIPT_DIR/augment-memory-integration.py",
        "mcp-server"
      ],
      "env": {
        "OPENMEMORY_URL": "http://localhost:8765",
        "USER_ID": "$(whoami)"
      }
    }
  }
}
EOF

    # Create project template
    cat > "$augment_config_dir/project-template.json" << EOF
{
  "mcp_endpoint": "http://localhost:8765/mcp/cursor/sse/$(whoami)",
  "auto_memory_storage": true,
  "memory_categories": {
    "preferences": true,
    "project": true,
    "solutions": true,
    "patterns": true,
    "decisions": true
  }
}
EOF

    print_success "Global Augment configuration created"
    print_success "Template available at $augment_config_dir/project-template.json"
}

# Function to test the live cloning setup
test_live_cloning() {
    print_header "Testing live cloning setup..."
    
    # Check if OpenMemory is running
    if ! curl -s "http://localhost:8765/docs" > /dev/null; then
        print_error "OpenMemory is not running. Start it with: ./start-openmemory.sh start"
        return 1
    fi
    
    print_success "OpenMemory is running"
    
    # Test the monitor script
    print_header "Running test scan..."
    python3 "$SCRIPT_DIR/augment-memory-monitor.py" --scan-once
    
    if [ $? -eq 0 ]; then
        print_success "Monitor test completed successfully"
    else
        print_warning "Monitor test had issues (this may be normal if no new memories found)"
    fi
    
    # Test memory storage
    print_header "Testing memory storage..."
    "$SCRIPT_DIR/remember.sh" "Live cloning test - $(date)" test
    
    if [ $? -eq 0 ]; then
        print_success "Memory storage test passed"
    else
        print_error "Memory storage test failed"
        return 1
    fi
}

# Function to show status
show_status() {
    print_header "Live Cloning Status"
    
    # Check OpenMemory
    if curl -s "http://localhost:8765/docs" > /dev/null; then
        print_success "OpenMemory Server: Running"
    else
        print_error "OpenMemory Server: Not Running"
    fi
    
    # Check monitor service
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if launchctl list | grep -q "com.augment.memory.monitor"; then
            print_success "Monitor Service: Running"
        else
            print_warning "Monitor Service: Not Running"
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if systemctl --user is-active augment-memory-monitor >/dev/null 2>&1; then
            print_success "Monitor Service: Running"
        else
            print_warning "Monitor Service: Not Running"
        fi
    fi
    
    # Check configuration
    if [ -f "$HOME/.config/augment/mcp-servers.json" ]; then
        print_success "Global Augment Config: Configured"
    else
        print_warning "Global Augment Config: Not Configured"
    fi
}

# Function to show help
show_help() {
    echo "Live Augment Memory Cloning Setup"
    echo ""
    echo "Usage: $0 {install|test|status|help}"
    echo ""
    echo "Commands:"
    echo "  install  - Install live memory monitoring service"
    echo "  test     - Test the live cloning setup"
    echo "  status   - Show current status"
    echo "  help     - Show this help message"
    echo ""
    echo "What this does:"
    echo "  • Monitors ALL Augment data locations for new memories"
    echo "  • Automatically clones new memories to OpenMemory"
    echo "  • Runs continuously in the background"
    echo "  • Starts automatically on system boot"
    echo "  • Works across ALL projects and Augment instances"
}

# Main script logic
case "${1:-}" in
    install)
        print_header "Installing Live Augment Memory Cloning..."
        
        # Install monitor service
        install_monitor_service
        
        # Configure global Augment integration
        configure_augment_global
        
        print_header "Installation Summary:"
        print_success "✅ Live memory monitor installed and running"
        print_success "✅ Global Augment configuration created"
        print_success "✅ Automatic startup configured"
        
        echo ""
        print_header "What happens now:"
        echo "🔄 Monitor scans for new Augment memories every 30 seconds"
        echo "🧠 New memories are automatically cloned to OpenMemory"
        echo "🚀 Works across ALL projects and Augment sessions"
        echo "💾 Memories persist and sync across all your work"
        
        echo ""
        print_header "Next steps:"
        echo "1. Use Augment in any project - memories will be auto-cloned"
        echo "2. Check status with: $0 status"
        echo "3. View logs at: $SCRIPT_DIR/logs/monitor.log"
        ;;
    test)
        test_live_cloning
        ;;
    status)
        show_status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Invalid command: ${1:-}"
        echo ""
        show_help
        exit 1
        ;;
esac
