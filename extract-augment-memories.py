#!/usr/bin/env python3
"""
Extract Augment Memories from Knowledge Graph
This script attempts to extract memories directly from Augment's knowledge graph
"""

import json
import sqlite3
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

class AugmentMemoryExtractor:
    def __init__(self):
        self.memories = []
        self.entities = []
        self.relations = []
        
    def find_augment_data_files(self) -> List[str]:
        """Find potential Augment data files"""
        possible_locations = [
            # Common Augment data locations
            os.path.expanduser("~/.augment/"),
            os.path.expanduser("~/Library/Application Support/Augment/"),
            os.path.expanduser("~/AppData/Local/Augment/"),
            os.path.expanduser("~/Documents/Augment/"),
            # Current directory and subdirectories
            ".",
            "./data/",
            "./augment_data/",
        ]
        
        found_files = []
        
        for location in possible_locations:
            if os.path.exists(location):
                for root, dirs, files in os.walk(location):
                    for file in files:
                        if any(ext in file.lower() for ext in ['.db', '.sqlite', '.json', '.txt']):
                            full_path = os.path.join(root, file)
                            found_files.append(full_path)
        
        return found_files
    
    def extract_from_sqlite(self, db_path: str) -> bool:
        """Extract memories from SQLite database"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"📊 Found tables in {db_path}: {[t[0] for t in tables]}")
            
            # Look for memory-related tables
            memory_tables = []
            for table in tables:
                table_name = table[0].lower()
                if any(keyword in table_name for keyword in ['memory', 'entity', 'node', 'knowledge', 'graph']):
                    memory_tables.append(table[0])
            
            if memory_tables:
                print(f"🧠 Found potential memory tables: {memory_tables}")
                
                for table in memory_tables:
                    try:
                        cursor.execute(f"SELECT * FROM {table} LIMIT 10")
                        rows = cursor.fetchall()
                        
                        # Get column names
                        cursor.execute(f"PRAGMA table_info({table})")
                        columns = [col[1] for col in cursor.fetchall()]
                        
                        print(f"📋 Table {table} columns: {columns}")
                        print(f"📊 Sample data: {len(rows)} rows")
                        
                        # Extract data based on table structure
                        for row in rows:
                            row_dict = dict(zip(columns, row))
                            self._process_database_row(table, row_dict)
                            
                    except Exception as e:
                        print(f"⚠️  Error reading table {table}: {e}")
            
            conn.close()
            return len(memory_tables) > 0
            
        except Exception as e:
            print(f"⚠️  Error reading SQLite file {db_path}: {e}")
            return False
    
    def extract_from_json(self, json_path: str) -> bool:
        """Extract memories from JSON file"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📄 Processing JSON file: {json_path}")
            
            # Handle different JSON structures
            if isinstance(data, dict):
                if 'entities' in data:
                    self.entities.extend(data['entities'])
                if 'relations' in data:
                    self.relations.extend(data['relations'])
                if 'memories' in data:
                    self.memories.extend(data['memories'])
                    
                # Look for other potential memory structures
                for key, value in data.items():
                    if isinstance(value, list) and key.lower() in ['nodes', 'items', 'data']:
                        for item in value:
                            if isinstance(item, dict):
                                self._process_json_item(item)
            
            elif isinstance(data, list):
                for item in data:
                    if isinstance(item, dict):
                        self._process_json_item(item)
            
            return True
            
        except Exception as e:
            print(f"⚠️  Error reading JSON file {json_path}: {e}")
            return False
    
    def _process_database_row(self, table_name: str, row_data: Dict[str, Any]):
        """Process a database row and extract memory information"""
        memory_item = {
            "source": f"database_{table_name}",
            "data": row_data,
            "extracted_at": datetime.now().isoformat()
        }
        
        # Try to extract meaningful fields
        name = None
        content = None
        memory_type = "database_record"
        
        for key, value in row_data.items():
            key_lower = key.lower()
            if key_lower in ['name', 'title', 'label']:
                name = str(value)
            elif key_lower in ['content', 'text', 'description', 'value']:
                content = str(value)
            elif key_lower in ['type', 'category', 'kind']:
                memory_type = str(value)
        
        if name or content:
            memory_item.update({
                "name": name or f"Record_{len(self.memories)}",
                "content": content or str(row_data),
                "type": memory_type
            })
            self.memories.append(memory_item)
    
    def _process_json_item(self, item: Dict[str, Any]):
        """Process a JSON item and extract memory information"""
        if 'name' in item or 'title' in item or 'content' in item:
            memory_item = {
                "source": "json_file",
                "data": item,
                "extracted_at": datetime.now().isoformat(),
                "name": item.get('name', item.get('title', f"Item_{len(self.memories)}")),
                "content": item.get('content', item.get('description', str(item))),
                "type": item.get('type', item.get('category', 'json_record'))
            }
            self.memories.append(memory_item)
    
    def extract_from_text_files(self, file_path: str) -> bool:
        """Extract memories from text files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for structured data in text files
            lines = content.split('\n')
            current_memory = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Look for memory markers
                if any(marker in line.lower() for marker in ['memory:', 'entity:', 'note:', 'remember:']):
                    if current_memory:
                        self.memories.append(current_memory)
                    
                    current_memory = {
                        "source": f"text_file_{os.path.basename(file_path)}",
                        "name": line,
                        "content": line,
                        "type": "text_record",
                        "extracted_at": datetime.now().isoformat()
                    }
                elif current_memory and line:
                    current_memory["content"] += "\n" + line
            
            if current_memory:
                self.memories.append(current_memory)
            
            return True
            
        except Exception as e:
            print(f"⚠️  Error reading text file {file_path}: {e}")
            return False
    
    def extract_all_memories(self) -> Dict[str, Any]:
        """Extract memories from all available sources"""
        print("🔍 Searching for Augment data files...")
        
        data_files = self.find_augment_data_files()
        
        if not data_files:
            print("⚠️  No potential Augment data files found")
            return {"memories": [], "entities": [], "relations": []}
        
        print(f"📁 Found {len(data_files)} potential data files")
        
        processed_files = 0
        for file_path in data_files:
            print(f"\n📄 Processing: {file_path}")
            
            try:
                if file_path.lower().endswith(('.db', '.sqlite')):
                    if self.extract_from_sqlite(file_path):
                        processed_files += 1
                elif file_path.lower().endswith('.json'):
                    if self.extract_from_json(file_path):
                        processed_files += 1
                elif file_path.lower().endswith('.txt'):
                    if self.extract_from_text_files(file_path):
                        processed_files += 1
                        
            except Exception as e:
                print(f"⚠️  Error processing {file_path}: {e}")
        
        print(f"\n📊 Extraction Summary:")
        print(f"📁 Processed files: {processed_files}")
        print(f"🧠 Extracted memories: {len(self.memories)}")
        print(f"🏷️  Extracted entities: {len(self.entities)}")
        print(f"🔗 Extracted relations: {len(self.relations)}")
        
        return {
            "memories": self.memories,
            "entities": self.entities,
            "relations": self.relations,
            "extraction_summary": {
                "processed_files": processed_files,
                "total_items": len(self.memories) + len(self.entities) + len(self.relations),
                "extracted_at": datetime.now().isoformat()
            }
        }
    
    def save_extracted_data(self, output_file: str = "extracted_augment_memories.json"):
        """Save extracted data to JSON file"""
        data = self.extract_all_memories()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved extracted data to: {output_file}")
        return output_file

def main():
    print("🔍 Augment Memory Extractor")
    print("=" * 50)
    
    extractor = AugmentMemoryExtractor()
    
    # Extract all available memories
    output_file = extractor.save_extracted_data()
    
    # Show summary
    data = extractor.extract_all_memories()
    total_items = len(data["memories"]) + len(data["entities"]) + len(data["relations"])
    
    if total_items > 0:
        print(f"\n✅ Successfully extracted {total_items} items")
        print(f"📄 Data saved to: {output_file}")
        print("\n🚀 Next step: Run complete-augment-migration.py to migrate to OpenMemory")
    else:
        print("\n⚠️  No memories found in automatic extraction")
        print("💡 You may need to manually export your Augment data")
        print("💡 Or run complete-augment-migration.py which includes manual memories")

if __name__ == "__main__":
    main()
