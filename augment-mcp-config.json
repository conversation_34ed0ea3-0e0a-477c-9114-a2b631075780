{"mcpServers": {"openmemory": {"command": "npx", "args": ["@modelcontextprotocol/server-fetch", "http://localhost:8765/mcp/cursor/sse/YOUR_USERNAME"], "env": {"NODE_ENV": "production"}}}, "tools": {"openmemory": {"description": "OpenMemory MCP Server for persistent memory across AI sessions", "endpoint": "http://localhost:8765/mcp/cursor/sse/YOUR_USERNAME", "capabilities": ["add_memories", "search_memory", "list_memories", "delete_all_memories"]}}, "augment_integration": {"auto_memory_storage": {"enabled": true, "triggers": ["user_preferences", "project_context", "important_decisions", "code_patterns", "debugging_solutions"], "memory_categories": {"preferences": {"description": "User coding preferences, style guides, and personal choices", "auto_store": true}, "project": {"description": "Project-specific context, architecture, and decisions", "auto_store": true}, "solutions": {"description": "Problem-solving approaches and debugging solutions", "auto_store": true}, "patterns": {"description": "Code patterns, best practices, and reusable solutions", "auto_store": false}}}, "memory_retrieval": {"enabled": true, "context_injection": {"automatic": true, "relevance_threshold": 0.7, "max_memories": 5}, "search_enhancement": {"enabled": true, "semantic_search": true, "keyword_boost": true}}, "privacy": {"local_only": true, "encryption": false, "data_retention": "indefinite", "exclude_patterns": ["password", "api_key", "secret", "token", "credential"]}}, "configuration": {"server_url": "http://localhost:8765", "frontend_url": "http://localhost:3000", "health_check_interval": 30, "retry_attempts": 3, "timeout": 10000}}