#!/usr/bin/env python3
"""
Augment Memory Integration
Provides automatic memory storage and retrieval for Augment AI sessions
"""

import requests
import json
import os
import sys
import time
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

class AugmentMemoryIntegration:
    def __init__(self, base_url: str = "http://localhost:8765", user_id: str = None):
        self.base_url = base_url
        self.user_id = user_id or os.getenv('USER', 'augment_user')
        self.session_id = f"augment_{int(time.time())}"
        
        # Memory triggers - patterns that indicate important information
        self.memory_triggers = {
            'preferences': [
                r'i prefer', r'i like', r'i dislike', r'i always', r'i never',
                r'my preference', r'i tend to', r'i usually', r'i typically'
            ],
            'project': [
                r'this project', r'the project', r'our architecture', r'we use',
                r'the codebase', r'our stack', r'project structure', r'requirements'
            ],
            'solution': [
                r'the solution', r'fixed by', r'solved by', r'the fix',
                r'to resolve', r'the workaround', r'debugging', r'error fix'
            ],
            'pattern': [
                r'best practice', r'pattern', r'approach', r'methodology',
                r'convention', r'standard', r'guideline', r'principle'
            ],
            'decision': [
                r'decided to', r'chose to', r'went with', r'selected',
                r'decision', r'choice', r'opted for'
            ]
        }
    
    def is_server_running(self) -> bool:
        """Check if OpenMemory server is running"""
        try:
            response = requests.get(f"{self.base_url}/docs", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def add_memory(self, content: str, category: str = "general", metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Add a memory to OpenMemory"""
        if not self.is_server_running():
            return {"error": "OpenMemory server is not running"}

        url = f"{self.base_url}/api/v1/memories/"

        # Prepare metadata
        memory_metadata = {
            "category": category,
            "source": "augment",
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            **(metadata or {})
        }

        payload = {
            "user_id": self.user_id,
            "text": content,
            "metadata": memory_metadata,
            "app": "augment"
        }
        
        try:
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}
    
    def search_memories(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search memories in OpenMemory"""
        if not self.is_server_running():
            return []

        url = f"{self.base_url}/api/v1/memories/"

        params = {
            "user_id": self.user_id,
            "search_query": query,
            "size": limit
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            result = response.json()
            # Extract items from paginated response
            if isinstance(result, dict) and 'items' in result:
                return result['items']
            return result if isinstance(result, list) else []
        except requests.exceptions.RequestException as e:
            return []
    
    def get_relevant_context(self, query: str, max_memories: int = 3) -> str:
        """Get relevant context for a query"""
        memories = self.search_memories(query, limit=max_memories)
        
        if not memories:
            return ""
        
        context_parts = []
        for memory in memories:
            if isinstance(memory, dict) and "memory" in memory:
                context_parts.append(f"• {memory['memory']}")
        
        if context_parts:
            return f"Relevant context from your memory:\n" + "\n".join(context_parts)
        return ""
    
    def auto_categorize_content(self, content: str) -> str:
        """Automatically categorize content based on patterns"""
        content_lower = content.lower()
        
        for category, patterns in self.memory_triggers.items():
            for pattern in patterns:
                if re.search(pattern, content_lower):
                    return category
        
        return "general"
    
    def should_store_memory(self, content: str) -> bool:
        """Determine if content should be automatically stored as memory"""
        content_lower = content.lower()
        
        # Check for memory trigger patterns
        for patterns in self.memory_triggers.values():
            for pattern in patterns:
                if re.search(pattern, content_lower):
                    return True
        
        # Check for other indicators
        memory_indicators = [
            r'remember', r'note that', r'important', r'keep in mind',
            r'for future', r'next time', r'always', r'never'
        ]
        
        for indicator in memory_indicators:
            if re.search(indicator, content_lower):
                return True
        
        return False
    
    def process_conversation(self, user_input: str, ai_response: str = None) -> Dict[str, Any]:
        """Process a conversation turn and automatically store relevant memories"""
        results = {
            "memories_stored": [],
            "context_retrieved": "",
            "suggestions": []
        }
        
        # Check if user input should be stored as memory
        if self.should_store_memory(user_input):
            category = self.auto_categorize_content(user_input)
            memory_result = self.add_memory(
                user_input, 
                category, 
                {"type": "user_input", "auto_stored": True}
            )
            if "error" not in memory_result:
                results["memories_stored"].append({
                    "content": user_input,
                    "category": category,
                    "type": "user_input"
                })
        
        # Check if AI response contains memorable information
        if ai_response and self.should_store_memory(ai_response):
            category = self.auto_categorize_content(ai_response)
            memory_result = self.add_memory(
                ai_response, 
                category, 
                {"type": "ai_response", "auto_stored": True}
            )
            if "error" not in memory_result:
                results["memories_stored"].append({
                    "content": ai_response,
                    "category": category,
                    "type": "ai_response"
                })
        
        # Get relevant context for the conversation
        context = self.get_relevant_context(user_input)
        if context:
            results["context_retrieved"] = context
        
        return results
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get a summary of stored memories"""
        if not self.is_server_running():
            return {"error": "OpenMemory server is not running"}
        
        try:
            url = f"{self.base_url}/api/v1/memories/"
            response = requests.get(url, params={"user_id": self.user_id}, timeout=10)
            response.raise_for_status()
            memories = response.json()
            
            # Categorize memories
            categories = {}
            for memory in memories:
                if isinstance(memory, dict) and "metadata" in memory:
                    category = memory["metadata"].get("category", "general")
                    if category not in categories:
                        categories[category] = 0
                    categories[category] += 1
            
            return {
                "total_memories": len(memories),
                "categories": categories,
                "recent_memories": memories[:5] if memories else []
            }
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}

# CLI interface
def main():
    if len(sys.argv) < 2:
        print("Augment Memory Integration")
        print("Usage:")
        print("  python3 augment-memory-integration.py store <content> [category]")
        print("  python3 augment-memory-integration.py search <query> [limit]")
        print("  python3 augment-memory-integration.py context <query>")
        print("  python3 augment-memory-integration.py process <user_input> [ai_response]")
        print("  python3 augment-memory-integration.py summary")
        print("  python3 augment-memory-integration.py status")
        sys.exit(1)
    
    integration = AugmentMemoryIntegration()
    command = sys.argv[1]
    
    if command == "store":
        if len(sys.argv) < 3:
            print("Error: Content required")
            sys.exit(1)
        content = sys.argv[2]
        category = sys.argv[3] if len(sys.argv) > 3 else integration.auto_categorize_content(content)
        result = integration.add_memory(content, category)
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(f"Memory stored successfully in category: {category}")
    
    elif command == "search":
        if len(sys.argv) < 3:
            print("Error: Query required")
            sys.exit(1)
        query = sys.argv[2]
        limit = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        memories = integration.search_memories(query, limit)
        print(json.dumps(memories, indent=2))
    
    elif command == "context":
        if len(sys.argv) < 3:
            print("Error: Query required")
            sys.exit(1)
        query = sys.argv[2]
        context = integration.get_relevant_context(query)
        print(context if context else "No relevant context found")
    
    elif command == "process":
        if len(sys.argv) < 3:
            print("Error: User input required")
            sys.exit(1)
        user_input = sys.argv[2]
        ai_response = sys.argv[3] if len(sys.argv) > 3 else None
        result = integration.process_conversation(user_input, ai_response)
        print(json.dumps(result, indent=2))
    
    elif command == "summary":
        summary = integration.get_memory_summary()
        print(json.dumps(summary, indent=2))
    
    elif command == "status":
        if integration.is_server_running():
            print("✅ OpenMemory server is running")
            print(f"🔗 Endpoint: {integration.base_url}")
            print(f"👤 User: {integration.user_id}")
        else:
            print("❌ OpenMemory server is not running")
            print("💡 Start it with: ./start-openmemory.sh start")
    
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
