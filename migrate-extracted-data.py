#!/usr/bin/env python3
"""
Migrate extracted Augment data to OpenMemory
"""

import json
import requests
import time
from datetime import datetime

# Configuration
OPENMEMORY_API_URL = "http://localhost:8765"
USER_ID = "deepakbatham"

def add_memory_to_openmemory(text: str, metadata: dict = None) -> bool:
    """Add a memory to OpenMemory"""
    url = f"{OPENMEMORY_API_URL}/api/v1/memories/"
    
    payload = {
        "user_id": USER_ID,
        "text": text,
        "metadata": metadata or {},
        "app": "augment_migration"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        if response.status_code in [200, 201]:
            return True
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def migrate_extracted_data():
    """Migrate data from extracted_augment_memories.json"""
    print("🔄 Migrating extracted Augment data...")
    
    try:
        with open("extracted_augment_memories.json", "r") as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ extracted_augment_memories.json not found")
        return False
    
    memories = data.get("memories", [])
    entities = data.get("entities", [])
    relations = data.get("relations", [])
    
    total_items = len(memories) + len(entities) + len(relations)
    migrated_count = 0
    
    print(f"📊 Found {total_items} items to migrate")
    
    # Migrate memories
    for i, memory in enumerate(memories, 1):
        print(f"\n🧠 Migrating memory {i}/{len(memories)}: {memory.get('name', 'Unknown')[:50]}...")
        
        memory_text = f"""Extracted Memory: {memory.get('name', 'Unknown')}

Content:
{memory.get('content', 'No content')}

Source: {memory.get('source', 'Unknown')}
Extracted at: {memory.get('extracted_at', 'Unknown')}

[Migrated from Augment extraction on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]"""
        
        metadata = {
            "source": "augment_extraction",
            "original_source": memory.get("source", "unknown"),
            "extraction_date": memory.get("extracted_at"),
            "migration_date": datetime.now().isoformat(),
            "memory_type": memory.get("type", "extracted_memory")
        }
        
        if add_memory_to_openmemory(memory_text, metadata):
            print(f"✅ Migrated memory: {memory.get('name', 'Unknown')[:50]}")
            migrated_count += 1
        else:
            print(f"❌ Failed to migrate memory: {memory.get('name', 'Unknown')[:50]}")
        
        time.sleep(2)
    
    # Migrate entities
    for i, entity in enumerate(entities, 1):
        print(f"\n🏷️  Migrating entity {i}/{len(entities)}: {entity.get('name', 'Unknown')}")
        
        entity_text = f"""Entity: {entity.get('name', 'Unknown')}

Type: {entity.get('entityType', 'Unknown')}

Observations:
{chr(10).join(f"- {obs}" for obs in entity.get('observations', []))}

[Migrated from Augment extraction on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]"""
        
        metadata = {
            "source": "augment_extraction",
            "entity_name": entity.get("name"),
            "entity_type": entity.get("entityType"),
            "migration_date": datetime.now().isoformat(),
            "memory_type": "entity"
        }
        
        if add_memory_to_openmemory(entity_text, metadata):
            print(f"✅ Migrated entity: {entity.get('name', 'Unknown')}")
            migrated_count += 1
        else:
            print(f"❌ Failed to migrate entity: {entity.get('name', 'Unknown')}")
        
        time.sleep(2)
    
    # Migrate relations
    for i, relation in enumerate(relations, 1):
        print(f"\n🔗 Migrating relation {i}/{len(relations)}: {relation.get('from', 'Unknown')} → {relation.get('to', 'Unknown')}")
        
        relation_text = f"""Relationship: {relation.get('from', 'Unknown')} {relation.get('relationType', 'relates to')} {relation.get('to', 'Unknown')}

This represents a connection between entities in the knowledge base.

[Migrated from Augment extraction on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]"""
        
        metadata = {
            "source": "augment_extraction",
            "relation_from": relation.get("from"),
            "relation_to": relation.get("to"),
            "relation_type": relation.get("relationType"),
            "migration_date": datetime.now().isoformat(),
            "memory_type": "relation"
        }
        
        if add_memory_to_openmemory(relation_text, metadata):
            print(f"✅ Migrated relation: {relation.get('from', 'Unknown')} → {relation.get('to', 'Unknown')}")
            migrated_count += 1
        else:
            print(f"❌ Failed to migrate relation: {relation.get('from', 'Unknown')} → {relation.get('to', 'Unknown')}")
        
        time.sleep(2)
    
    print(f"\n📊 Migration Summary:")
    print(f"✅ Successfully migrated: {migrated_count}/{total_items}")
    print(f"📈 Success rate: {(migrated_count/total_items)*100:.1f}%")
    
    return migrated_count > 0

def main():
    print("🔄 Migrating Extracted Augment Data to OpenMemory")
    print("=" * 60)
    
    # Check connection
    try:
        response = requests.get(f"{OPENMEMORY_API_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ Cannot connect to OpenMemory API")
            return False
    except:
        print("❌ Cannot connect to OpenMemory API")
        return False
    
    print("✅ Connected to OpenMemory API")
    
    success = migrate_extracted_data()
    
    if success:
        print("\n🎉 Migration completed!")
        print("🌐 View your memories at: http://localhost:3000")
    else:
        print("\n❌ Migration failed")
    
    return success

if __name__ == "__main__":
    main()
